(()=>{var a={};a.id=974,a.ids=[974],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},597:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>f});var d=c(7413),e=c(9273);function f(){return(0,d.jsx)(e.RealtimeAssistant,{})}},846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},1313:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(5239),e=c(8088),f=c(7220),g=c(1289),h=c(6191),i=c(4823),j=c(1998),k=c(2603),l=c(4649),m=c(2781),n=c(2602),o=c(1268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(6713),u=c(3365),v=c(1454),w=c(7778),x=c(6143),y=c(9105),z=c(8171),A=c(6439),B=c(6133),C=c.n(B),D=c(893),E=c(2836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,597)),"/Users/<USER>/work/Instrument-2/sys_design_instrument/app/page.tsx"]}]},{layout:[()=>Promise.resolve().then(c.bind(c,8014)),"/Users/<USER>/work/Instrument-2/sys_design_instrument/app/layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,6133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,9868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,9615,23)),"next/dist/client/components/builtin/unauthorized.js"]}],H=["/Users/<USER>/work/Instrument-2/sys_design_instrument/app/page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},2704:()=>{},2756:(a,b,c)=>{Promise.resolve().then(c.bind(c,2898))},2898:(a,b,c)=>{"use strict";c.d(b,{RealtimeAssistant:()=>Q});var d=c(687),e=c(3210);function f(a,b){return function(c){let d=(0,e.useContext)(a);if(!c?.optional&&!d)throw Error(`This component must be used within ${b}.`);return d}}function g(a,b){function c(c){let d=a(c);return d?d[b]:null}return{[b]:function(a){let b,d=!1;"function"==typeof a?b=a:a&&"object"==typeof a&&(d=!!a.optional,b=a.selector);let e=c({optional:d});return e?b?e(b):e():null},[`${b}Store`]:c}}var h=a=>{a.__isBound||(a.__internal_bindMethods?.(),a.__isBound=!0)};function i(a){return function(b){let c,d=!1;"function"==typeof b?c=b:b&&(d=!!b.optional,c=b.selector);let f=a({optional:d});return f?function(a,b=a=>a){h(a);let c=(0,e.useSyncExternalStore)(a.subscribe,()=>b(a.getState()),()=>b(a.getState()));return(0,e.useDebugValue)(c),c}(f,c):null}}var j=(0,e.createContext)(null),k=f(j,"AssistantRuntimeProvider"),{useToolUIs:l,useToolUIsStore:m}=g(k,"useToolUIs");i(a=>(function(a){let b=k(a);return b?b.useAssistantRuntime():null})(a)?.threads??null);let n=a=>{let b,c=new Set,d=(a,d)=>{let e="function"==typeof a?a(b):a;if(!Object.is(e,b)){let a=b;b=(null!=d?d:"object"!=typeof e||null===e)?e:Object.assign({},b,e),c.forEach(c=>c(b,a))}},e=()=>b,f={setState:d,getState:e,getInitialState:()=>g,subscribe:a=>(c.add(a),()=>c.delete(a))},g=b=a(d,e,f);return f},o=a=>{let b=(a=>a?n(a):n)(a),c=a=>(function(a,b=a=>a){let c=e.useSyncExternalStore(a.subscribe,e.useCallback(()=>b(a.getState()),[a,b]),e.useCallback(()=>b(a.getInitialState()),[a,b]));return e.useDebugValue(c),c})(b,a);return Object.assign(c,b),c},p=a=>a?o(a):o;var q=(0,e.createContext)(null),r=f(q,"AssistantRuntimeProvider");function s(a){let b=r(a);return b?b.useThreadRuntime():null}i(s),i(a=>s(a)?.composer??null);var t=(0,e.createContext)(null),u=f(t,"a component passed to <ThreadListPrimitive.Items components={...}>");i(function(a){let b=u(a);return b?b.useThreadListItemRuntime():null});var v=({runtime:a,children:b})=>{let c=(a=>{let[b]=(0,e.useState)(()=>p(()=>a));return(0,e.useEffect)(()=>{h(a),b.setState(a,!0)},[a,b]),b})(a),[f]=(0,e.useState)(()=>({useThreadListItemRuntime:c}));return(0,d.jsx)(t.Provider,{value:f,children:b})},w=(0,e.createContext)(null),{useThreadViewport:x,useThreadViewportStore:y}=g(f(w,"ThreadPrimitive.Viewport"),"useThreadViewport"),z=({children:a})=>{let b=(()=>{let a=y({optional:!0}),[b]=(0,e.useState)(()=>(()=>{let a=new Set;return p(()=>({isAtBottom:!0,scrollToBottom:()=>{for(let b of a)b()},onScrollToBottom:b=>(a.add(b),()=>{a.delete(b)})}))})());return(0,e.useEffect)(()=>a?.getState().onScrollToBottom(()=>{b.getState().scrollToBottom()}),[a,b]),(0,e.useEffect)(()=>{if(a)return b.subscribe(b=>{a.getState().isAtBottom!==b.isAtBottom&&a.setState({isAtBottom:b.isAtBottom})})},[b,a]),b})(),[c]=(0,e.useState)(()=>({useThreadViewport:b}));return(0,d.jsx)(w.Provider,{value:c,children:a})},A=({children:a,listItemRuntime:b,runtime:c})=>{let f=(a=>{let[b]=(0,e.useState)(()=>p(()=>a));return(0,e.useEffect)(()=>{h(a),h(a.composer),b.setState(a,!0)},[a,b]),b})(c),[g]=(0,e.useState)(()=>({useThreadRuntime:f}));return(0,d.jsx)(v,{runtime:b,children:(0,d.jsx)(q.Provider,{value:g,children:(0,d.jsx)(z,{children:a})})})},B=(0,e.memo)(({children:a,runtime:b})=>{let c=(a=>{let[b]=(0,e.useState)(()=>p(()=>a));return(0,e.useEffect)(()=>{h(a),h(a.threads),b.setState(a,!0)},[a,b]),b})(b),f=(0,e.useMemo)(()=>p(a=>{let b=new Map;return Object.freeze({getToolUI:a=>{let c=b.get(a);return c?.at(-1)||null},setToolUI:(c,d)=>{let e=b.get(c);return e||(e=[],b.set(c,e)),e.push(d),a({}),()=>{let b=e.indexOf(d);-1!==b&&e.splice(b,1),b===e.length&&a({})}}})}),[]),[g]=(0,e.useState)(()=>({useToolUIs:f,useAssistantRuntime:c})),i=b._core?.RenderComponent;return(0,d.jsxs)(j.Provider,{value:g,children:[i&&(0,d.jsx)(i,{}),(0,d.jsx)(A,{runtime:b.thread,listItemRuntime:b.threads.mainItem,children:a})]})});class C{constructor(a){this.ws=null,this.isConnected=!1,this.sessionStartTime=0,this.SESSION_TIMEOUT=18e5,this.timeoutId=null,this.config=a}async connect(){try{let a=await fetch("/api/realtime/ephemeral",{method:"POST"});if(!a.ok)throw Error(`Failed to get ephemeral key: ${await a.text()}`);let{ephemeralKey:b}=await a.json(),c=`wss://api.openai.com/v1/realtime?model=${this.config.model||"gpt-4o-realtime-preview-2024-10-01"}`;this.ws=new WebSocket(c,["realtime",`Bearer.${b}`,"openai-beta.realtime-v1"]),this.ws.onopen=()=>{this.isConnected=!0,this.sessionStartTime=Date.now(),this.startSessionTimeout(),this.initializeSession(),this.config.onConnect?.()},this.ws.onmessage=a=>{try{let b=JSON.parse(a.data);this.handleEvent(b)}catch(a){console.error("Failed to parse WebSocket message:",a)}},this.ws.onclose=()=>{this.isConnected=!1,this.clearSessionTimeout(),this.config.onDisconnect?.()},this.ws.onerror=a=>{this.config.onError?.(Error("WebSocket error"))}}catch(a){this.config.onError?.(a)}}initializeSession(){this.sendEvent({type:"session.update",session:{modalities:["text","audio"],voice:this.config.voice||"alloy",instructions:this.config.instructions||"You are a helpful assistant.",turn_detection:null,input_audio_transcription:{model:"whisper-1"},tools:[]}})}handleEvent(a){this.config.onEvent?.(a)}sendEvent(a){this.ws&&this.isConnected&&this.ws.send(JSON.stringify(a))}appendAudioBuffer(a){this.sendEvent({type:"input_audio_buffer.append",audio:a})}commitAudioBuffer(){this.sendEvent({type:"input_audio_buffer.commit"})}clearAudioBuffer(){this.sendEvent({type:"input_audio_buffer.clear"})}createResponse(){this.sendEvent({type:"response.create"})}cancelResponse(){this.sendEvent({type:"response.cancel"})}startSessionTimeout(){this.timeoutId=setTimeout(()=>{this.reconnect()},this.SESSION_TIMEOUT)}clearSessionTimeout(){this.timeoutId&&(clearTimeout(this.timeoutId),this.timeoutId=null)}async reconnect(){this.disconnect(),await new Promise(a=>setTimeout(a,1e3)),await this.connect()}disconnect(){this.clearSessionTimeout(),this.ws&&(this.ws.close(),this.ws=null),this.isConnected=!1}getConnectionStatus(){return this.isConnected}getSessionDuration(){return this.sessionStartTime?Date.now()-this.sessionStartTime:0}}let D=a=>{let b=a.replace(/^([A-Z])|[\s-_]+(\w)/g,(a,b,c)=>c?c.toUpperCase():b.toLowerCase());return b.charAt(0).toUpperCase()+b.slice(1)},E=(...a)=>a.filter((a,b,c)=>!!a&&""!==a.trim()&&c.indexOf(a)===b).join(" ").trim();var F={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let G=(0,e.forwardRef)(({color:a="currentColor",size:b=24,strokeWidth:c=2,absoluteStrokeWidth:d,className:f="",children:g,iconNode:h,...i},j)=>(0,e.createElement)("svg",{ref:j,...F,width:b,height:b,stroke:a,strokeWidth:d?24*Number(c)/Number(b):c,className:E("lucide",f),...!g&&!(a=>{for(let b in a)if(b.startsWith("aria-")||"role"===b||"title"===b)return!0})(i)&&{"aria-hidden":"true"},...i},[...h.map(([a,b])=>(0,e.createElement)(a,b)),...Array.isArray(g)?g:[g]])),H=(a,b)=>{let c=(0,e.forwardRef)(({className:c,...d},f)=>(0,e.createElement)(G,{ref:f,iconNode:b,className:E(`lucide-${D(a).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()}`,`lucide-${a}`,c),...d}));return c.displayName=D(a),c},I=H("volume-x",[["path",{d:"M11 4.702a.705.705 0 0 0-1.203-.498L6.413 7.587A1.4 1.4 0 0 1 5.416 8H3a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h2.416a1.4 1.4 0 0 1 .997.413l3.383 3.384A.705.705 0 0 0 11 19.298z",key:"uqj9uw"}],["line",{x1:"22",x2:"16",y1:"9",y2:"15",key:"1ewh16"}],["line",{x1:"16",x2:"22",y1:"9",y2:"15",key:"5ykzw1"}]]),J=H("volume-2",[["path",{d:"M11 4.702a.705.705 0 0 0-1.203-.498L6.413 7.587A1.4 1.4 0 0 1 5.416 8H3a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h2.416a1.4 1.4 0 0 1 .997.413l3.383 3.384A.705.705 0 0 0 11 19.298z",key:"uqj9uw"}],["path",{d:"M16 9a5 5 0 0 1 0 6",key:"1q6k2b"}],["path",{d:"M19.364 18.364a9 9 0 0 0 0-12.728",key:"ijwkga"}]]);function K({isMuted:a,playbackRate:b,onMuteToggle:c,onPlaybackRateChange:e}){return(0,d.jsxs)("div",{className:"flex items-center gap-4",children:[(0,d.jsx)("button",{onClick:c,className:`p-2 rounded-md transition-colors ${a?"bg-red-100 text-red-700 hover:bg-red-200":"bg-gray-100 text-gray-700 hover:bg-gray-200"}`,title:a?"Unmute (M)":"Mute (M)",children:a?(0,d.jsx)(I,{size:20}):(0,d.jsx)(J,{size:20})}),(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Speed:"}),(0,d.jsx)("select",{value:b,onChange:a=>e(parseFloat(a.target.value)),className:"px-2 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500",children:[.5,.75,1,1.25,1.5,2].map(a=>(0,d.jsxs)("option",{value:a,children:[a,"x"]},a))})]}),(0,d.jsx)("div",{className:"text-xs text-gray-500",children:a?"Audio muted":`Playing at ${b}x speed`})]})}function L({isConnected:a,isConnecting:b,onConnect:c,onDisconnect:e}){return(0,d.jsxs)("div",{className:"flex items-center gap-3",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("div",{className:`w-3 h-3 rounded-full ${a?"bg-green-500":b?"bg-yellow-500 animate-pulse":"bg-red-500"}`}),(0,d.jsx)("span",{className:"text-sm font-medium",children:a?"Connected":b?"Connecting...":"Disconnected"})]}),(0,d.jsx)("button",{onClick:a?e:c,disabled:b,className:`px-4 py-2 rounded-md text-sm font-medium transition-colors ${a?"bg-red-100 text-red-700 hover:bg-red-200":"bg-blue-100 text-blue-700 hover:bg-blue-200"} disabled:opacity-50 disabled:cursor-not-allowed`,children:b?"Connecting...":a?"Disconnect":"Connect"})]})}let M=H("mic",[["path",{d:"M12 19v3",key:"npa21l"}],["path",{d:"M19 10v2a7 7 0 0 1-14 0v-2",key:"1vc78b"}],["rect",{x:"9",y:"2",width:"6",height:"13",rx:"3",key:"s6n7sd"}]]),N=H("mic-off",[["path",{d:"M12 19v3",key:"npa21l"}],["path",{d:"M15 9.34V5a3 3 0 0 0-5.68-1.33",key:"1gzdoj"}],["path",{d:"M16.95 16.95A7 7 0 0 1 5 12v-2",key:"cqa7eg"}],["path",{d:"M18.89 13.23A7 7 0 0 0 19 12v-2",key:"16hl24"}],["path",{d:"m2 2 20 20",key:"1ooewy"}],["path",{d:"M9 9v3a3 3 0 0 0 5.12 2.12",key:"r2i35w"}]]);function O({onTrigger:a,disabled:b,isRecording:c}){return(0,d.jsxs)("div",{className:"flex items-center gap-3",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[c?(0,d.jsx)(M,{className:"w-4 h-4 text-green-600"}):(0,d.jsx)(N,{className:"w-4 h-4 text-gray-400"}),(0,d.jsx)("span",{className:"text-sm text-gray-600",children:c?"Listening...":"Not recording"})]}),(0,d.jsx)("button",{onClick:a,disabled:b,className:`px-6 py-3 rounded-lg font-medium transition-all ${b?"bg-gray-200 text-gray-400 cursor-not-allowed":"bg-blue-600 text-white hover:bg-blue-700 active:bg-blue-800 shadow-md hover:shadow-lg"}`,title:"Trigger AI Response (Spacebar)",children:"\uD83C\uDFA4 Ask AI"})]})}!function(){var a=Error("Cannot find module '@assistant-ui/react/primitives'");throw a.code="MODULE_NOT_FOUND",a}();let P=`You are an expert system design interviewer. Listen to the conversation between the interviewee and interviewer but only respond with questions, clarifications, or feedback when the interviewee says "AI, your turn" or similar trigger phrases. 

Maintain context across exchanges and provide insightful questions that help evaluate the candidate's system design thinking. Focus on:
- Scalability considerations
- Trade-offs and design decisions
- Clarifying requirements
- Probing deeper into architectural choices
- Identifying potential bottlenecks

Keep responses concise and interview-appropriate.`;function Q(){let[a,b]=(0,e.useState)(!1),[c,f]=(0,e.useState)(!1),[g,h]=(0,e.useState)(null),[i,j]=(0,e.useState)(!1),[k,l]=(0,e.useState)(!1),[m,n]=(0,e.useState)(1),o=(0,e.useRef)(null),p=(0,e.useRef)(null),q=(0,e.useRef)(null),r=(0,e.useCallback)(a=>{switch(console.log("Realtime event:",a),q.current?.handleRealtimeEvent(a),a.type){case"session.created":console.log("Session created");break;case"input_audio_buffer.speech_started":console.log("Speech started");break;case"input_audio_buffer.speech_stopped":console.log("Speech stopped");break;case"response.audio.delta":a.delta&&p.current&&p.current.playAudioDelta(a.delta);break;case"error":h(`Realtime API error: ${a.error?.message||"Unknown error"}`)}},[]),s=(0,e.useCallback)(async()=>{if(!c&&!a){f(!0),h(null);try{let a=new C({apiKey:"",model:"gpt-4o-realtime-preview-2024-10-01",voice:"alloy",instructions:P,onEvent:r,onError:a=>h(a.message),onConnect:()=>{b(!0),f(!1),u()},onDisconnect:()=>{b(!1),j(!1)}});await a.connect(),o.current=a}catch(a){h(a.message),f(!1)}}},[c,a,r]),t=(0,e.useCallback)(()=>{o.current?.disconnect(),o.current=null,b(!1),j(!1)},[]),u=(0,e.useCallback)(()=>{p.current&&o.current&&!i&&(p.current.startRecording(a=>{o.current?.appendAudioBuffer(a)}),j(!0))},[i]),v=(0,e.useCallback)(()=>{o.current&&a&&(o.current.commitAudioBuffer(),o.current.createResponse())},[a]),w=(0,e.useCallback)(()=>{let a=!k;l(a),p.current?.setMuted(a)},[k]),x=(0,e.useCallback)(a=>{n(a),p.current?.setPlaybackRate(a)},[]);return(0,d.jsx)(B,{runtime:q.current,children:(0,d.jsx)("div",{className:"flex h-screen bg-gray-50",children:(0,d.jsxs)("div",{className:"flex-1 flex flex-col",children:[(0,d.jsxs)("div",{className:"bg-white border-b px-6 py-4 flex items-center justify-between",children:[(0,d.jsx)("h1",{className:"text-xl font-semibold",children:"Realtime Interview Assistant"}),(0,d.jsx)(L,{isConnected:a,isConnecting:c,onConnect:s,onDisconnect:t})]}),g&&(0,d.jsxs)("div",{className:"bg-red-50 border-l-4 border-red-400 p-4 mx-6 mt-4",children:[(0,d.jsx)("div",{className:"text-red-700",children:g}),(0,d.jsx)("button",{onClick:()=>h(null),className:"text-red-600 hover:text-red-800 text-sm mt-2",children:"Dismiss"})]}),(0,d.jsx)("div",{className:"flex-1 overflow-hidden",children:(0,d.jsx)(Object(function(){var a=Error("Cannot find module '@assistant-ui/react/primitives'");throw a.code="MODULE_NOT_FOUND",a}()),{})}),(0,d.jsxs)("div",{className:"bg-white border-t p-4",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)(K,{isMuted:k,playbackRate:m,onMuteToggle:w,onPlaybackRateChange:x}),(0,d.jsx)(O,{onTrigger:v,disabled:!a,isRecording:i})]}),(0,d.jsxs)("div",{className:"text-xs text-gray-500 mt-2 text-center",children:["Press ",(0,d.jsx)("kbd",{className:"px-1 py-0.5 bg-gray-100 rounded",children:"Space"})," to trigger AI response • Press ",(0,d.jsx)("kbd",{className:"px-1 py-0.5 bg-gray-100 rounded",children:"M"})," to mute/unmute"]})]})]})})})}},3033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:a=>{"use strict";a.exports=require("path")},6439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},6713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},7639:(a,b,c)=>{Promise.resolve().then(c.bind(c,9273))},8014:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>f,metadata:()=>e});var d=c(7413);c(2704);let e={title:"Realtime Interview Assistant",description:"Speech-to-speech assistant for mock system design interviews"};function f({children:a}){return(0,d.jsx)("html",{lang:"en",children:(0,d.jsx)("body",{className:"antialiased",children:a})})}},8045:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,6133,23)),Promise.resolve().then(c.t.bind(c,6444,23)),Promise.resolve().then(c.t.bind(c,6042,23)),Promise.resolve().then(c.t.bind(c,9477,23)),Promise.resolve().then(c.t.bind(c,9345,23)),Promise.resolve().then(c.t.bind(c,2089,23)),Promise.resolve().then(c.t.bind(c,6577,23)),Promise.resolve().then(c.t.bind(c,1307,23)),Promise.resolve().then(c.t.bind(c,4817,23))},8189:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,5227,23)),Promise.resolve().then(c.t.bind(c,6346,23)),Promise.resolve().then(c.t.bind(c,7924,23)),Promise.resolve().then(c.t.bind(c,99,23)),Promise.resolve().then(c.t.bind(c,8243,23)),Promise.resolve().then(c.t.bind(c,8827,23)),Promise.resolve().then(c.t.bind(c,2763,23)),Promise.resolve().then(c.t.bind(c,7173,23)),Promise.resolve().then(c.bind(c,5587))},8354:a=>{"use strict";a.exports=require("util")},9121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9273:(a,b,c)=>{"use strict";c.d(b,{RealtimeAssistant:()=>d});let d=(0,c(1369).registerClientReference)(function(){throw Error("Attempted to call RealtimeAssistant() from the server but RealtimeAssistant is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/work/Instrument-2/sys_design_instrument/components/realtime-assistant.tsx","RealtimeAssistant")},9294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9402:()=>{},9570:()=>{}};var b=require("../webpack-runtime.js");b.C(a);var c=b.X(0,[985,769],()=>b(b.s=1313));module.exports=c})();