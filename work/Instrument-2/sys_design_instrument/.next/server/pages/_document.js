"use strict";(()=>{var a={};a.id=220,a.ids=[220],a.modules={361:a=>{a.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},3873:a=>{a.exports=require("path")},4396:a=>{a.exports=require("react")},6472:a=>{a.exports=require("@opentelemetry/api")},8732:a=>{a.exports=require("react/jsx-runtime")}};var b=require("../webpack-runtime.js");b.C(a);var c=b.X(0,[548],()=>b(b.s=8548));module.exports=c})();