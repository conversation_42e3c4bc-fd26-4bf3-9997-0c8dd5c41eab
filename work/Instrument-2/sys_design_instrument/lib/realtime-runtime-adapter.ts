"use client";

import { AssistantRuntime, ThreadMessage } from "@assistant-ui/react";
import { RealtimeWebSocket, RealtimeEvent } from "./realtime-websocket";

export interface RealtimeMessage {
  id: string;
  role: "user" | "assistant";
  content: string;
  timestamp: number;
  type: "text" | "audio";
}

export class RealtimeRuntimeAdapter implements AssistantRuntime {
  private messages: RealtimeMessage[] = [];
  private subscribers = new Set<() => void>();
  private wsRef: React.MutableRefObject<RealtimeWebSocket | null>;
  private messageIdCounter = 0;
  private currentAssistantMessage = "";

  constructor(wsRef: React.MutableRefObject<RealtimeWebSocket | null>) {
    this.wsRef = wsRef;
  }

  // Subscribe to runtime changes
  subscribe(callback: () => void): () => void {
    this.subscribers.add(callback);
    return () => this.subscribers.delete(callback);
  }

  private notifySubscribers() {
    this.subscribers.forEach(callback => callback());
  }

  // Get current thread messages
  get messages(): ThreadMessage[] {
    return this.messages.map(msg => ({
      id: msg.id,
      role: msg.role,
      content: [{ type: "text", text: msg.content }],
      createdAt: new Date(msg.timestamp),
    }));
  }

  // Add message to thread
  addMessage(message: RealtimeMessage) {
    this.messages.push(message);
    this.notifySubscribers();
  }

  // Update current assistant message (for streaming)
  updateCurrentAssistantMessage(content: string) {
    this.currentAssistantMessage = content;
    
    // Find or create current assistant message
    const lastMessage = this.messages[this.messages.length - 1];
    if (lastMessage && lastMessage.role === "assistant" && lastMessage.id.startsWith("temp-")) {
      // Update existing temporary message
      lastMessage.content = content;
    } else {
      // Create new temporary message
      this.messages.push({
        id: `temp-assistant-${this.messageIdCounter++}`,
        role: "assistant",
        content,
        timestamp: Date.now(),
        type: "text",
      });
    }
    
    this.notifySubscribers();
  }

  // Finalize current assistant message
  finalizeAssistantMessage() {
    const lastMessage = this.messages[this.messages.length - 1];
    if (lastMessage && lastMessage.role === "assistant" && lastMessage.id.startsWith("temp-")) {
      // Convert temporary message to permanent
      lastMessage.id = `assistant-${this.messageIdCounter++}`;
    }
    this.currentAssistantMessage = "";
    this.notifySubscribers();
  }

  // Handle realtime events
  handleRealtimeEvent(event: RealtimeEvent) {
    switch (event.type) {
      case "conversation.item.input_audio_transcription.completed":
        // Add user message
        this.addMessage({
          id: `user-${this.messageIdCounter++}`,
          role: "user",
          content: event.transcript,
          timestamp: Date.now(),
          type: "text",
        });
        break;

      case "response.audio_transcript.delta":
        // Update streaming assistant message
        this.updateCurrentAssistantMessage(
          this.currentAssistantMessage + (event.delta || "")
        );
        break;

      case "response.audio_transcript.done":
        // Finalize assistant message
        this.finalizeAssistantMessage();
        break;

      case "response.done":
        // Ensure message is finalized
        this.finalizeAssistantMessage();
        break;
    }
  }

  // Assistant-UI runtime interface methods
  async append(message: { role: "user"; content: string }): Promise<void> {
    // Add user message to our local state
    this.addMessage({
      id: `user-${this.messageIdCounter++}`,
      role: "user",
      content: message.content,
      timestamp: Date.now(),
      type: "text",
    });

    // Note: In realtime mode, we don't automatically trigger responses
    // The user needs to use the manual trigger
  }

  async reload(): Promise<void> {
    // In realtime mode, we don't support reload
    // The conversation is managed by the WebSocket connection
  }

  async cancel(): Promise<void> {
    // Cancel current response
    this.wsRef.current?.cancelResponse();
  }

  // Thread management
  get threadId(): string {
    return "realtime-thread";
  }

  get isRunning(): boolean {
    return this.wsRef.current?.getConnectionStatus() || false;
  }

  // Clear conversation
  clearMessages() {
    this.messages = [];
    this.currentAssistantMessage = "";
    this.notifySubscribers();
  }

  // Get message count
  get messageCount(): number {
    return this.messages.length;
  }
}
