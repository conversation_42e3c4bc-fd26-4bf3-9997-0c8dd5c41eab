"use client";

import { use<PERSON><PERSON>back, useEffect, useRef, useState } from "react";
import { AssistantRuntimeProvider } from "@assistant-ui/react";
import { RealtimeWebSocket, RealtimeEvent } from "@/lib/realtime-websocket";
import { AudioPipeline } from "@/lib/audio-pipeline";
import { RealtimeRuntimeAdapter } from "@/lib/realtime-runtime-adapter";
import { AudioControls } from "./audio-controls";
import { ConnectionStatus } from "./connection-status";
import { TriggerButton } from "./trigger-button";
import { SimpleThread } from "./simple-thread";

// Interview-specific system prompt
const INTERVIEW_SYSTEM_PROMPT = `You are an expert system design interviewer. Listen to the conversation between the interviewee and interviewer but only respond with questions, clarifications, or feedback when the interviewee says "AI, your turn" or similar trigger phrases. 

Maintain context across exchanges and provide insightful questions that help evaluate the candidate's system design thinking. Focus on:
- Scalability considerations
- Trade-offs and design decisions
- Clarifying requirements
- Probing deeper into architectural choices
- Identifying potential bottlenecks

Keep responses concise and interview-appropriate.`;

export function RealtimeAssistant() {
  // Connection state
  const [isConnected, setIsConnected] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Audio state
  const [isRecording, setIsRecording] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const [playbackRate, setPlaybackRate] = useState(1.0);

  // Refs
  const wsRef = useRef<RealtimeWebSocket | null>(null);
  const audioPipelineRef = useRef<AudioPipeline | null>(null);
  const runtimeAdapterRef = useRef<RealtimeRuntimeAdapter | null>(null);

  // Initialize runtime adapter
  useEffect(() => {
    runtimeAdapterRef.current = new RealtimeRuntimeAdapter(wsRef);
  }, []);

  // Initialize audio pipeline
  useEffect(() => {
    const initAudio = async () => {
      try {
        const pipeline = new AudioPipeline({
          sampleRate: 24000,
          channels: 1,
          echoCancellation: true,
        });

        pipeline.setOnError((error) => {
          setError(`Audio error: ${error.message}`);
        });

        await pipeline.initialize();
        audioPipelineRef.current = pipeline;
      } catch (error) {
        setError(`Failed to initialize audio: ${(error as Error).message}`);
      }
    };

    initAudio();

    return () => {
      audioPipelineRef.current?.destroy();
    };
  }, []);

  // WebSocket event handler
  const handleRealtimeEvent = useCallback((event: RealtimeEvent) => {
    console.log("Realtime event:", event);

    // Forward event to runtime adapter
    runtimeAdapterRef.current?.handleRealtimeEvent(event);

    switch (event.type) {
      case "session.created":
        console.log("Session created");
        break;

      case "input_audio_buffer.speech_started":
        console.log("Speech started");
        break;

      case "input_audio_buffer.speech_stopped":
        console.log("Speech stopped");
        break;

      case "response.audio.delta":
        // Play audio delta
        if (event.delta && audioPipelineRef.current) {
          audioPipelineRef.current.playAudioDelta(event.delta);
        }
        break;

      case "error":
        setError(`Realtime API error: ${event.error?.message || "Unknown error"}`);
        break;

      default:
        // Handle other events as needed
        break;
    }
  }, []);

  // Connect to realtime API
  const connect = useCallback(async () => {
    if (isConnecting || isConnected) return;

    setIsConnecting(true);
    setError(null);

    try {
      const ws = new RealtimeWebSocket({
        apiKey: "", // Will be handled by ephemeral endpoint
        model: "gpt-4o-realtime-preview-2024-10-01",
        voice: "alloy",
        instructions: INTERVIEW_SYSTEM_PROMPT,
        onEvent: handleRealtimeEvent,
        onError: (error) => setError(error.message),
        onConnect: () => {
          setIsConnected(true);
          setIsConnecting(false);
          startRecording();
        },
        onDisconnect: () => {
          setIsConnected(false);
          setIsRecording(false);
        },
      });

      await ws.connect();
      wsRef.current = ws;

    } catch (error) {
      setError((error as Error).message);
      setIsConnecting(false);
    }
  }, [isConnecting, isConnected, handleRealtimeEvent]);

  // Disconnect
  const disconnect = useCallback(() => {
    wsRef.current?.disconnect();
    wsRef.current = null;
    setIsConnected(false);
    setIsRecording(false);
  }, []);

  // Start continuous recording
  const startRecording = useCallback(() => {
    if (!audioPipelineRef.current || !wsRef.current || isRecording) return;

    audioPipelineRef.current.startRecording((audioData) => {
      wsRef.current?.appendAudioBuffer(audioData);
    });

    setIsRecording(true);
  }, [isRecording]);

  // Manual trigger for AI response
  const triggerResponse = useCallback(() => {
    if (!wsRef.current || !isConnected) return;

    // Commit the audio buffer and request response
    wsRef.current.commitAudioBuffer();
    wsRef.current.createResponse();
  }, [isConnected]);

  // Audio controls
  const handleMuteToggle = useCallback(() => {
    const newMuted = !isMuted;
    setIsMuted(newMuted);
    audioPipelineRef.current?.setMuted(newMuted);
  }, [isMuted]);

  const handlePlaybackRateChange = useCallback((rate: number) => {
    setPlaybackRate(rate);
    audioPipelineRef.current?.setPlaybackRate(rate);
  }, []);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyPress = (event: KeyboardEvent) => {
      if (event.code === "Space" && !event.repeat) {
        event.preventDefault();
        triggerResponse();
      } else if (event.code === "KeyM" && !event.repeat) {
        event.preventDefault();
        handleMuteToggle();
      }
    };

    window.addEventListener("keydown", handleKeyPress);
    return () => window.removeEventListener("keydown", handleKeyPress);
  }, [triggerResponse, handleMuteToggle]);

  if (!runtimeAdapterRef.current) {
    return <div>Loading...</div>;
  }

  return (
    <div className="flex h-screen bg-gray-50">
      {/* Main chat area */}
      <div className="flex-1 flex flex-col">
        {/* Header */}
        <div className="bg-white border-b px-6 py-4 flex items-center justify-between">
          <h1 className="text-xl font-semibold">Realtime Interview Assistant</h1>
          <ConnectionStatus
            isConnected={isConnected}
            isConnecting={isConnecting}
            onConnect={connect}
            onDisconnect={disconnect}
          />
        </div>

        {/* Error display */}
        {error && (
          <div className="bg-red-50 border-l-4 border-red-400 p-4 mx-6 mt-4">
            <div className="text-red-700">{error}</div>
            <button
              onClick={() => setError(null)}
              className="text-red-600 hover:text-red-800 text-sm mt-2"
            >
              Dismiss
            </button>
          </div>
        )}

        {/* Chat Thread */}
        <SimpleThread runtime={runtimeAdapterRef.current} />

          {/* Controls */}
          <div className="bg-white border-t p-4">
            <div className="flex items-center justify-between">
              <AudioControls
                isMuted={isMuted}
                playbackRate={playbackRate}
                onMuteToggle={handleMuteToggle}
                onPlaybackRateChange={handlePlaybackRateChange}
              />
              
              <TriggerButton
                onTrigger={triggerResponse}
                disabled={!isConnected}
                isRecording={isRecording}
              />
            </div>
            
            <div className="text-xs text-gray-500 mt-2 text-center">
              Press <kbd className="px-1 py-0.5 bg-gray-100 rounded">Space</kbd> to trigger AI response • 
              Press <kbd className="px-1 py-0.5 bg-gray-100 rounded">M</kbd> to mute/unmute
            </div>
          </div>
        </div>
      </div>
    </AssistantRuntimeProvider>
  );
}
